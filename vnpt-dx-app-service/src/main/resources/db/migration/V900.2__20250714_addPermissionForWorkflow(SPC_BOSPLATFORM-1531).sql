-- Thêm permission loại trạng thái
DELETE FROM vnpt_dev.permission WHERE code IN ('QUAN_LY_LOAI_TRANG_THAI', 'XEM_DANH_SACH_LOAI_TRANG_THAI', 'XEM_CHI_TIET_LOAI_TRANG_THAI', 'TAO_LOAI_TRANG_THAI',
                                               'CHINH_SUA_LOAI_TRANG_THAI', 'XOA_LOAI_TRANG_THAI');
DELETE FROM vnpt_dev.permission_portal WHERE permission_id NOT IN (SELECT id FROM vnpt_dev.permission);
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id NOT IN (SELECT id FROM vnpt_dev.permission);

-- Thêm permission group
INSERT INTO vnpt_dev.permission (id, name, code, parent_id, priority) VALUES
    (
        (SELECT max(id) + 1 from vnpt_dev.permission),
        'Quản lý loại trạng thái',
        'QUAN_LY_LOAI_TRANG_THAI',
        -1,
        (SELECT max(priority) + 1 from vnpt_dev.permission)
    );
-- Thêm permission
INSERT INTO vnpt_dev.permission (id, name, code, parent_id, priority) VALUES
    (
      (SELECT max(id) + 1 from vnpt_dev.permission),
      'Xem danh sách loại trạng thái',
      'XEM_DANH_SACH_LOAI_TRANG_THAI',
      (SELECT id from vnpt_dev.permission WHERE code = 'QUAN_LY_LOAI_TRANG_THAI'),
      (SELECT max(priority) + 1 from vnpt_dev.permission)
    ),
    (
      (SELECT max(id) + 2 from vnpt_dev.permission),
      'Xem chi tiết loại trạng thái',
      'XEM_CHI_TIET_LOAI_TRANG_THAI',
      (SELECT id from vnpt_dev.permission WHERE code = 'QUAN_LY_LOAI_TRANG_THAI'),
      (SELECT max(priority) + 2 from vnpt_dev.permission)
    ),
    (
      (SELECT max(id) + 3 from vnpt_dev.permission),
      'Tạo loại trạng thái',
      'TAO_LOAI_TRANG_THAI',
      (SELECT id from vnpt_dev.permission WHERE code = 'QUAN_LY_LOAI_TRANG_THAI'),
      (SELECT max(priority) + 3 from vnpt_dev.permission)
    ),
    (
      (SELECT max(id) + 4 from vnpt_dev.permission),
      'Chỉnh sửa loại trạng thái',
      'CHINH_SUA_LOAI_TRANG_THAI',
      (SELECT id from vnpt_dev.permission WHERE code = 'QUAN_LY_LOAI_TRANG_THAI'),
      (SELECT max(priority) + 4 from vnpt_dev.permission)
    ),
    (
      (SELECT max(id) + 5 from vnpt_dev.permission),
      'Xóa loại trạng thái',
      'XOA_LOAI_TRANG_THAI',
      (SELECT id from vnpt_dev.permission WHERE code = 'QUAN_LY_LOAI_TRANG_THAI'),
      (SELECT max(priority) + 5 from vnpt_dev.permission)
    );

-- Thêm permission vào admin portal
INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
    (
      (SELECT max(id) + 1 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'QUAN_LY_LOAI_TRANG_THAI'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
    ),
    (
      (SELECT max(id) + 2 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_LOAI_TRANG_THAI'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
    ),
    (
      (SELECT max(id) + 3 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'XEM_CHI_TIET_LOAI_TRANG_THAI'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
    ),
    (
      (SELECT max(id) + 4 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'TAO_LOAI_TRANG_THAI'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
    ),
    (
      (SELECT max(id) + 5 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'CHINH_SUA_LOAI_TRANG_THAI'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
    ),
    (
      (SELECT max(id) + 6 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'XOA_LOAI_TRANG_THAI'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
    );


-- Thêm permission vào role FULL_ADMIN
INSERT INTO vnpt_dev.roles_permissions (id, role_id, permission_id, allow_edit) VALUES
    (
        (SELECT max(id) + 1 from vnpt_dev.roles_permissions),
        (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
        (select id from vnpt_dev.permission WHERE code = 'QUAN_LY_LOAI_TRANG_THAI'),
        0
    ),
    (
        (SELECT max(id) + 2 from vnpt_dev.roles_permissions),
        (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
        (select id from vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_LOAI_TRANG_THAI'),
        0
    ),
    (
        (SELECT max(id) + 3 from vnpt_dev.roles_permissions),
        (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
        (select id from vnpt_dev.permission WHERE code = 'XEM_CHI_TIET_LOAI_TRANG_THAI'),
        0
    ),
    (
        (SELECT max(id) + 4 from vnpt_dev.roles_permissions),
        (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
        (select id from vnpt_dev.permission WHERE code = 'TAO_LOAI_TRANG_THAI'),
        0
    ),
    (
        (SELECT max(id) + 5 from vnpt_dev.roles_permissions),
        (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
        (select id from vnpt_dev.permission WHERE code = 'CHINH_SUA_LOAI_TRANG_THAI'),
        0
    ),
    (
        (SELECT max(id) + 6 from vnpt_dev.roles_permissions),
        (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
        (select id from vnpt_dev.permission WHERE code = 'XOA_LOAI_TRANG_THAI'),
        0
    );

-- Thêm permission trạng thái
DELETE FROM vnpt_dev.permission WHERE code IN ('QUAN_LY_TRANG_THAI', 'XEM_DANH_SACH_TRANG_THAI', 'XEM_CHI_TIET_TRANG_THAI', 'TAO_TRANG_THAI',
                                               'CHINH_SUA_TRANG_THAI', 'XOA_TRANG_THAI');
DELETE FROM vnpt_dev.permission_portal WHERE permission_id NOT IN (SELECT id FROM vnpt_dev.permission);
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id NOT IN (SELECT id FROM vnpt_dev.permission);

-- Thêm permission group
INSERT INTO vnpt_dev.permission (id, name, code, parent_id, priority) VALUES
    (
        (SELECT max(id) + 1 from vnpt_dev.permission),
        'Quản lý trạng thái',
        'QUAN_LY_TRANG_THAI',
        -1,
        (SELECT max(priority) + 1 from vnpt_dev.permission)
    );
-- Thêm permission
INSERT INTO vnpt_dev.permission (id, name, code, parent_id, priority) VALUES
    (
      (SELECT max(id) + 1 from vnpt_dev.permission),
      'Xem danh sách trạng thái',
      'XEM_DANH_SACH_TRANG_THAI',
      (SELECT id from vnpt_dev.permission WHERE code = 'QUAN_LY_TRANG_THAI'),
      (SELECT max(priority) + 1 from vnpt_dev.permission)
    ),
    (
      (SELECT max(id) + 2 from vnpt_dev.permission),
      'Xem chi tiết trạng thái',
      'XEM_CHI_TIET_TRANG_THAI',
      (SELECT id from vnpt_dev.permission WHERE code = 'QUAN_LY_TRANG_THAI'),
      (SELECT max(priority) + 2 from vnpt_dev.permission)
    ),
    (
      (SELECT max(id) + 3 from vnpt_dev.permission),
      'Tạo trạng thái',
      'TAO_TRANG_THAI',
      (SELECT id from vnpt_dev.permission WHERE code = 'QUAN_LY_TRANG_THAI'),
      (SELECT max(priority) + 3 from vnpt_dev.permission)
    ),
    (
      (SELECT max(id) + 4 from vnpt_dev.permission),
      'Chỉnh sửa trạng thái',
      'CHINH_SUA_TRANG_THAI',
      (SELECT id from vnpt_dev.permission WHERE code = 'QUAN_LY_TRANG_THAI'),
      (SELECT max(priority) + 4 from vnpt_dev.permission)
    ),
    (
      (SELECT max(id) + 5 from vnpt_dev.permission),
      'Xóa trạng thái',
      'XOA_TRANG_THAI',
      (SELECT id from vnpt_dev.permission WHERE code = 'QUAN_LY_TRANG_THAI'),
      (SELECT max(priority) + 5 from vnpt_dev.permission)
    );

-- Thêm permission vào admin portal
INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
    (
      (SELECT max(id) + 1 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'QUAN_LY_TRANG_THAI'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
    ),
    (
      (SELECT max(id) + 2 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_TRANG_THAI'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
    ),
    (
      (SELECT max(id) + 3 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'XEM_CHI_TIET_TRANG_THAI'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
    ),
    (
      (SELECT max(id) + 4 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'TAO_TRANG_THAI'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
    ),
    (
      (SELECT max(id) + 5 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'CHINH_SUA_TRANG_THAI'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
    ),
    (
      (SELECT max(id) + 6 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'XOA_TRANG_THAI'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
    );


-- Thêm permission vào role FULL_ADMIN
INSERT INTO vnpt_dev.roles_permissions (id, role_id, permission_id, allow_edit) VALUES
    (
        (SELECT max(id) + 1 from vnpt_dev.roles_permissions),
        (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
        (select id from vnpt_dev.permission WHERE code = 'QUAN_LY_TRANG_THAI'),
        0
    ),
    (
        (SELECT max(id) + 2 from vnpt_dev.roles_permissions),
        (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
        (select id from vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_TRANG_THAI'),
        0
    ),
    (
        (SELECT max(id) + 3 from vnpt_dev.roles_permissions),
        (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
        (select id from vnpt_dev.permission WHERE code = 'XEM_CHI_TIET_TRANG_THAI'),
        0
    ),
    (
        (SELECT max(id) + 4 from vnpt_dev.roles_permissions),
        (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
        (select id from vnpt_dev.permission WHERE code = 'TAO_TRANG_THAI'),
        0
    ),
    (
        (SELECT max(id) + 5 from vnpt_dev.roles_permissions),
        (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
        (select id from vnpt_dev.permission WHERE code = 'CHINH_SUA_TRANG_THAI'),
        0
    ),
    (
        (SELECT max(id) + 6 from vnpt_dev.roles_permissions),
        (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
        (select id from vnpt_dev.permission WHERE code = 'XOA_TRANG_THAI'),
        0
    );

-- Thêm permission cấu hình chuyển đổi trạng thái
DELETE FROM vnpt_dev.permission WHERE code IN ('CAU_HINH_CHUYEN_DOI_TRANG_THAI', 'XEM_DANH_SACH_CAU_HINH_CHUYEN_DOI_TRANG_THAI',
                                               'XEM_CHI_TIET_CAU_HINH_CHUYEN_DOI_TRANG_THAI', 'TAO_CAU_HINH_CHUYEN_DOI_TRANG_THAI', 'CHINH_SUA_CAU_HINH_CHUYEN_DOI_TRANG_THAI', 'XOA_CAU_HINH_CHUYEN_DOI_TRANG_THAI');
DELETE FROM vnpt_dev.permission_portal WHERE permission_id NOT IN (SELECT id FROM vnpt_dev.permission);
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id NOT IN (SELECT id FROM vnpt_dev.permission);

-- Thêm permission group
INSERT INTO vnpt_dev.permission (id, name, code, parent_id, priority) VALUES
    (
        (SELECT max(id) + 1 from vnpt_dev.permission),
        'Cấu hình chuyển đổi trạng thái',
        'CAU_HINH_CHUYEN_DOI_TRANG_THAI',
        -1,
        (SELECT max(priority) + 1 from vnpt_dev.permission)
    );
-- Thêm permission
INSERT INTO vnpt_dev.permission (id, name, code, parent_id, priority) VALUES
    (
      (SELECT max(id) + 1 from vnpt_dev.permission),
      'Xem danh sách cấu hình chuyển đổi trạng thái',
      'XEM_DANH_SACH_CAU_HINH_CHUYEN_DOI_TRANG_THAI',
      (SELECT id from vnpt_dev.permission WHERE code = 'CAU_HINH_CHUYEN_DOI_TRANG_THAI'),
      (SELECT max(priority) + 1 from vnpt_dev.permission)
    ),
    (
      (SELECT max(id) + 2 from vnpt_dev.permission),
      'Xem chi tiết cấu hình chuyển đổi trạng thái',
      'XEM_CHI_TIET_CAU_HINH_CHUYEN_DOI_TRANG_THAI',
      (SELECT id from vnpt_dev.permission WHERE code = 'CAU_HINH_CHUYEN_DOI_TRANG_THAI'),
      (SELECT max(priority) + 2 from vnpt_dev.permission)
    ),
    (
      (SELECT max(id) + 3 from vnpt_dev.permission),
      'Tạo cấu hình chuyển đổi trạng thái',
      'TAO_CAU_HINH_CHUYEN_DOI_TRANG_THAI',
      (SELECT id from vnpt_dev.permission WHERE code = 'CAU_HINH_CHUYEN_DOI_TRANG_THAI'),
      (SELECT max(priority) + 3 from vnpt_dev.permission)
    ),
    (
      (SELECT max(id) + 4 from vnpt_dev.permission),
      'Chỉnh sửa cấu hình chuyển đổi trạng thái',
      'CHINH_SUA_CAU_HINH_CHUYEN_DOI_TRANG_THAI',
      (SELECT id from vnpt_dev.permission WHERE code = 'CAU_HINH_CHUYEN_DOI_TRANG_THAI'),
      (SELECT max(priority) + 4 from vnpt_dev.permission)
    ),
    (
      (SELECT max(id) + 5 from vnpt_dev.permission),
      'Xóa cấu hình chuyển đổi trạng thái',
      'XOA_CAU_HINH_CHUYEN_DOI_TRANG_THAI',
      (SELECT id from vnpt_dev.permission WHERE code = 'CAU_HINH_CHUYEN_DOI_TRANG_THAI'),
      (SELECT max(priority) + 5 from vnpt_dev.permission)
    );

-- Thêm permission vào admin portal
INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
    (
      (SELECT max(id) + 1 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'CAU_HINH_CHUYEN_DOI_TRANG_THAI'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
    ),
    (
      (SELECT max(id) + 2 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_CAU_HINH_CHUYEN_DOI_TRANG_THAI'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
    ),
    (
      (SELECT max(id) + 3 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'XEM_CHI_TIET_CAU_HINH_CHUYEN_DOI_TRANG_THAI'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
    ),
    (
      (SELECT max(id) + 4 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'TAO_CAU_HINH_CHUYEN_DOI_TRANG_THAI'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
    ),
    (
      (SELECT max(id) + 5 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'CHINH_SUA_CAU_HINH_CHUYEN_DOI_TRANG_THAI'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
    ),
    (
      (SELECT max(id) + 6 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'XOA_CAU_HINH_CHUYEN_DOI_TRANG_THAI'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
    );


-- Thêm permission vào role FULL_ADMIN
INSERT INTO vnpt_dev.roles_permissions (id, role_id, permission_id, allow_edit) VALUES
    (
        (SELECT max(id) + 1 from vnpt_dev.roles_permissions),
        (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
        (select id from vnpt_dev.permission WHERE code = 'CAU_HINH_CHUYEN_DOI_TRANG_THAI'),
        0
    ),
    (
        (SELECT max(id) + 2 from vnpt_dev.roles_permissions),
        (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
        (select id from vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_CAU_HINH_CHUYEN_DOI_TRANG_THAI'),
        0
    ),
    (
        (SELECT max(id) + 3 from vnpt_dev.roles_permissions),
        (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
        (select id from vnpt_dev.permission WHERE code = 'XEM_CHI_TIET_CAU_HINH_CHUYEN_DOI_TRANG_THAI'),
        0
    ),
    (
        (SELECT max(id) + 4 from vnpt_dev.roles_permissions),
        (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
        (select id from vnpt_dev.permission WHERE code = 'TAO_CAU_HINH_CHUYEN_DOI_TRANG_THAI'),
        0
    ),
    (
        (SELECT max(id) + 5 from vnpt_dev.roles_permissions),
        (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
        (select id from vnpt_dev.permission WHERE code = 'CHINH_SUA_CAU_HINH_CHUYEN_DOI_TRANG_THAI'),
        0
    ),
    (
        (SELECT max(id) + 6 from vnpt_dev.roles_permissions),
        (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
        (select id from vnpt_dev.permission WHERE code = 'XOA_CAU_HINH_CHUYEN_DOI_TRANG_THAI'),
        0
    );

-- Thêm permission tiến trình
DELETE FROM vnpt_dev.permission WHERE code IN ('QUAN_LY_TIEN_TRINH', 'XEM_DANH_SACH_TIEN_TRINH', 'XEM_CHI_TIET_TIEN_TRINH', 'TAO_TIEN_TRINH',
                                               'CHINH_SUA_TIEN_TRINH', 'XOA_TIEN_TRINH', 'KICH_HOAT_MAU');
DELETE FROM vnpt_dev.permission_portal WHERE permission_id NOT IN (SELECT id FROM vnpt_dev.permission);
DELETE FROM vnpt_dev.roles_permissions WHERE permission_id NOT IN (SELECT id FROM vnpt_dev.permission);

-- Thêm permission group
INSERT INTO vnpt_dev.permission (id, name, code, parent_id, priority) VALUES
    (
        (SELECT max(id) + 1 from vnpt_dev.permission),
        'Quản lý tiến trình',
        'QUAN_LY_TIEN_TRINH',
        -1,
        (SELECT max(priority) + 1 from vnpt_dev.permission)
    );
-- Thêm permission
INSERT INTO vnpt_dev.permission (id, name, code, parent_id, priority) VALUES
    (
      (SELECT max(id) + 1 from vnpt_dev.permission),
      'Xem danh sách tiến trình',
      'XEM_DANH_SACH_TIEN_TRINH',
      (SELECT id from vnpt_dev.permission WHERE code = 'QUAN_LY_TIEN_TRINH'),
      (SELECT max(priority) + 1 from vnpt_dev.permission)
    ),
    (
      (SELECT max(id) + 2 from vnpt_dev.permission),
      'Xem chi tiết tiến trình',
      'XEM_CHI_TIET_TIEN_TRINH',
      (SELECT id from vnpt_dev.permission WHERE code = 'QUAN_LY_TIEN_TRINH'),
      (SELECT max(priority) + 2 from vnpt_dev.permission)
    ),
    (
      (SELECT max(id) + 3 from vnpt_dev.permission),
      'Tạo tiến trình',
      'TAO_TIEN_TRINH',
      (SELECT id from vnpt_dev.permission WHERE code = 'QUAN_LY_TIEN_TRINH'),
      (SELECT max(priority) + 3 from vnpt_dev.permission)
    ),
    (
      (SELECT max(id) + 4 from vnpt_dev.permission),
      'Chỉnh sửa tiến trình',
      'CHINH_SUA_TIEN_TRINH',
      (SELECT id from vnpt_dev.permission WHERE code = 'QUAN_LY_TIEN_TRINH'),
      (SELECT max(priority) + 4 from vnpt_dev.permission)
    ),
    (
      (SELECT max(id) + 5 from vnpt_dev.permission),
      'Xóa tiến trình',
      'XOA_TIEN_TRINH',
      (SELECT id from vnpt_dev.permission WHERE code = 'QUAN_LY_TIEN_TRINH'),
      (SELECT max(priority) + 5 from vnpt_dev.permission)
    ),
    (
      (SELECT max(id) + 6 from vnpt_dev.permission),
      'Kích hoạt mẫu',
      'KICH_HOAT_MAU',
      (SELECT id from vnpt_dev.permission WHERE code = 'QUAN_LY_TIEN_TRINH'),
      (SELECT max(priority) + 6 from vnpt_dev.permission)
    );

-- Thêm permission vào admin portal
INSERT INTO vnpt_dev.permission_portal (id, permission_id, portal_id) VALUES
    (
      (SELECT max(id) + 1 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'QUAN_LY_TIEN_TRINH'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
    ),
    (
      (SELECT max(id) + 2 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_TIEN_TRINH'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
    ),
    (
      (SELECT max(id) + 3 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'XEM_CHI_TIET_TIEN_TRINH'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
    ),
    (
      (SELECT max(id) + 4 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'TAO_TIEN_TRINH'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
    ),
    (
      (SELECT max(id) + 5 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'CHINH_SUA_TIEN_TRINH'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
    ),
    (
      (SELECT max(id) + 6 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'XOA_TIEN_TRINH'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
    ),
    (
      (SELECT max(id) + 7 from vnpt_dev.permission_portal),
      (SELECT id from vnpt_dev.permission WHERE code = 'KICH_HOAT_MAU'),
      (SELECT id from vnpt_dev.portal WHERE name = 'ADMIN')
    );


-- Thêm permission vào role FULL_ADMIN
INSERT INTO vnpt_dev.roles_permissions (id, role_id, permission_id, allow_edit) VALUES
    (
        (SELECT max(id) + 1 from vnpt_dev.roles_permissions),
        (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
        (select id from vnpt_dev.permission WHERE code = 'QUAN_LY_TIEN_TRINH'),
        0
    ),
    (
        (SELECT max(id) + 2 from vnpt_dev.roles_permissions),
        (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
        (select id from vnpt_dev.permission WHERE code = 'XEM_DANH_SACH_TIEN_TRINH'),
        0
    ),
    (
        (SELECT max(id) + 3 from vnpt_dev.roles_permissions),
        (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
        (select id from vnpt_dev.permission WHERE code = 'XEM_CHI_TIET_TIEN_TRINH'),
        0
    ),
    (
        (SELECT max(id) + 4 from vnpt_dev.roles_permissions),
        (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
        (select id from vnpt_dev.permission WHERE code = 'TAO_TIEN_TRINH'),
        0
    ),
    (
        (SELECT max(id) + 5 from vnpt_dev.roles_permissions),
        (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
        (select id from vnpt_dev.permission WHERE code = 'CHINH_SUA_TIEN_TRINH'),
        0
    ),
    (
        (SELECT max(id) + 6 from vnpt_dev.roles_permissions),
        (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
        (select id from vnpt_dev.permission WHERE code = 'XOA_TIEN_TRINH'),
        0
    ),
    (
        (SELECT max(id) + 7 from vnpt_dev.roles_permissions),
        (select id from vnpt_dev.role WHERE name = 'FULL_ADMIN'),
        (select id from vnpt_dev.permission WHERE code = 'KICH_HOAT_MAU'),
        0
    );

-- Cập nhật view phân quyền
REFRESH MATERIALIZED VIEW vnpt_dev.role_permission_api;

-- Đặt lại sequence để đảm bảo id tự tăng
CREATE SEQUENCE IF NOT EXISTS roles_permissions_id_seq;
ALTER TABLE "vnpt_dev"."roles_permissions" ALTER COLUMN id SET DEFAULT nextval('roles_permissions_id_seq');
ALTER SEQUENCE roles_permissions_id_seq OWNED BY roles_permissions.id;
SELECT setval('roles_permissions_id_seq', COALESCE(max(id), 0)) FROM roles_permissions;