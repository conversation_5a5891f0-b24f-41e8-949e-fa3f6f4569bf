-- C<PERSON><PERSON> nhật workflow_id mặc định cho đơn hàng chưa thuộc tiến trình nào
UPDATE vnpt_dev.product_orders
SET workflow_id = workflows.id
FROM vnpt_dev.workflows
WHERE workflows.is_default = TRUE
    AND ((product_orders.workflow_id IS NULL AND product_orders.workflow_step_id IS NULL)
        OR NOT EXISTS (
            SELECT 1
            FROM vnpt_dev.workflows
            WHERE workflows.id = product_orders.workflow_id
        )
    );

-- Cập nhật workflow_step_id theo order_progress
UPDATE vnpt_dev.product_orders
SET workflow_step_id = workflow_steps.id
FROM vnpt_dev.workflows workflows
    JOIN vnpt_dev.workflow_steps workflow_steps ON workflow_steps.workflow_id = workflows.id
WHERE workflows.is_default = TRUE
    AND product_orders.workflow_step_id IS NULL
    AND product_orders.workflow_id = workflows.id
    AND workflow_steps.index = product_orders.order_progress;


-- C<PERSON><PERSON> nhật cấu hình trạng thái và danh sách item cho thuê bao đơn hàng order
WITH items AS (
    SELECT
        sm.subscription_id,
        st.id AS state_transition_id,
        ARRAY_AGG(DISTINCT sti.id ORDER BY sti.id) AS item_ids
    FROM vnpt_dev.subscription_metadata sm
             JOIN vnpt_dev.subscriptions s ON sm.subscription_id = s.id
             JOIN vnpt_dev.services sv ON s.service_id = sv.id
             JOIN vnpt_dev.state_transitions st
                  ON (
                      (sv.classification = 1 AND st.object_type = 'DIGITAL') OR
                      (sv.classification = 2 AND st.object_type = 'SERVICE') OR
                      (sv.classification = 3 AND st.object_type = 'PHYSICAL')
                      )
             JOIN vnpt_dev.state_transition_items sti ON st.id = sti.state_transition_id
             LEFT JOIN vnpt_dev.state_transition_item_triggers trig ON trig.state_transition_item_id = sti.id
             LEFT JOIN vnpt_dev.states st_state ON st_state.id = sti.state_id
             LEFT JOIN vnpt_dev.states pre_state ON pre_state.id = trig.pre_state_id
    WHERE
        (sm.state_transition_id IS NULL
            OR NOT EXISTS (
                SELECT 1
                FROM vnpt_dev.state_transitions stx
                WHERE stx.id = sm.state_transition_id
            ))
      AND sm.product_order_id IS NOT NULL
      AND (
        -- PHYSICAL
        (sv.classification = 3 AND (
            (
                sv.installation_configuration = 1
                    AND st_state.name IN (
                                          'requested_warehouse', 'preparing_stock', 'ready_for_shipping',
                                          'pending_shipping', 'shipped', 'delivered', 'waiting_installation',
                                          'in_installation', 'installed', 'online_verified', 'cancelled'
                    )
                    AND (st_state.name != 'online_verified' OR pre_state.name = 'installed')
                )
                OR
            (
                sv.installation_configuration = 0
                    AND st_state.name IN (
                                          'requested_warehouse', 'preparing_stock', 'ready_for_shipping',
                                          'pending_shipping', 'shipped', 'delivered', 'online_verified', 'cancelled'
                    )
                    AND (st_state.name != 'online_verified' OR pre_state.name = 'delivered')
                )
            ))
            -- SERVICE/DIGITAL
            OR (sv.classification IN (1, 2) AND (
            -- on_os_type=0
            (sv.on_os_type = 0 AND (
                (sv.provider_type = 0 AND st_state.name IN ('vnpt_installing', 'vnpt_installed', 'vnpt_crash'))
                    OR
                (sv.provider_type = 1 AND st_state.name IN ('partner_installing', 'partner_installed', 'partner_crash'))
                ))
                -- on_os_type=1
                OR (sv.on_os_type = 1 AND (
                (sv.provider_type = 0 AND st_state.name IN (
                                                            'vnpt_os_saas_ordered', 'vnpt_os_saas_received',
                                                            'vnpt_os_saas_processing', 'vnpt_os_saas_completed',
                                                            'vnpt_os_saas_cancelled'
                    ))
                    OR
                (sv.provider_type = 2 AND st_state.name IN (
                                                            'third_party_os_saas_ordered', 'third_party_os_saas_received',
                                                            'third_party_os_saas_processing', 'third_party_os_saas_completed',
                                                            'third_party_os_saas_cancelled'
                    ))
                ))
            ))
        )
    GROUP BY sm.subscription_id, st.id
)
UPDATE vnpt_dev.subscription_metadata sm
SET state_transition_id = items.state_transition_id,
    state_transition_item_ids = items.item_ids
FROM items
WHERE sm.subscription_id = items.subscription_id;

--- Cập nhật trạng thái hiện tại của đơn hàng order theo luồng workflow
UPDATE vnpt_dev.subscription_metadata sm
SET state_id = CASE
    -- Nhóm 101 - 108
    WHEN sm.order_item_status = 101 THEN (SELECT id FROM vnpt_dev.states WHERE name = 'requested_warehouse')
    WHEN sm.order_item_status = 102 THEN (SELECT id FROM vnpt_dev.states WHERE name = 'preparing_stock')
    WHEN sm.order_item_status = 103 THEN (SELECT id FROM vnpt_dev.states WHERE name = 'ready_for_shipping')
    WHEN sm.order_item_status = 104 THEN (SELECT id FROM vnpt_dev.states WHERE name = 'pending_shipping')
    WHEN sm.order_item_status = 105 THEN (SELECT id FROM vnpt_dev.states WHERE name = 'shipped')
    WHEN sm.order_item_status = 106 THEN (SELECT id FROM vnpt_dev.states WHERE name = 'delivered')
    WHEN sm.order_item_status = 107 THEN (SELECT id FROM vnpt_dev.states WHERE name = 'online_verified')
    WHEN sm.order_item_status = 108 THEN (SELECT id FROM vnpt_dev.states WHERE name = 'cancelled')

    -- Nhóm 201 - 211
    WHEN sm.order_item_status = 201 THEN (SELECT id FROM vnpt_dev.states WHERE name = 'requested_warehouse')
    WHEN sm.order_item_status = 202 THEN (SELECT id FROM vnpt_dev.states WHERE name = 'preparing_stock')
    WHEN sm.order_item_status = 203 THEN (SELECT id FROM vnpt_dev.states WHERE name = 'ready_for_shipping')
    WHEN sm.order_item_status = 204 THEN (SELECT id FROM vnpt_dev.states WHERE name = 'pending_shipping')
    WHEN sm.order_item_status = 205 THEN (SELECT id FROM vnpt_dev.states WHERE name = 'shipped')
    WHEN sm.order_item_status = 206 THEN (SELECT id FROM vnpt_dev.states WHERE name = 'delivered')
    WHEN sm.order_item_status = 207 THEN (SELECT id FROM vnpt_dev.states WHERE name = 'waiting_installation')
    WHEN sm.order_item_status = 208 THEN (SELECT id FROM vnpt_dev.states WHERE name = 'in_installation')
    WHEN sm.order_item_status = 209 THEN (SELECT id FROM vnpt_dev.states WHERE name = 'installed')
    WHEN sm.order_item_status = 210 THEN (SELECT id FROM vnpt_dev.states WHERE name = 'online_verified')
    WHEN sm.order_item_status = 211 THEN (SELECT id FROM vnpt_dev.states WHERE name = 'cancelled')

    -- Nhóm 301 - 303 (phụ thuộc service) service thuộc loại SERVICE hoặc DIGITAL
    WHEN sm.order_item_status = 301 AND sv.classification IN (1,2) AND sv.on_os_type = 0 AND sv.provider_type = 0
        THEN (SELECT id FROM vnpt_dev.states WHERE name = 'vnpt_installing')
    WHEN sm.order_item_status = 301 AND sv.classification IN (1,2) AND sv.on_os_type = 0 AND sv.provider_type = 1
        THEN (SELECT id FROM vnpt_dev.states WHERE name = 'partner_installing')

    WHEN sm.order_item_status = 302 AND sv.classification IN (1,2) AND sv.on_os_type = 0 AND sv.provider_type = 0
        THEN (SELECT id FROM vnpt_dev.states WHERE name = 'vnpt_installed')
    WHEN sm.order_item_status = 302 AND sv.classification IN (1,2) AND sv.on_os_type = 0 AND sv.provider_type = 1
        THEN (SELECT id FROM vnpt_dev.states WHERE name = 'partner_installed')

    WHEN sm.order_item_status = 303 AND sv.classification IN (1,2) AND sv.on_os_type = 0 AND sv.provider_type = 0
        THEN (SELECT id FROM vnpt_dev.states WHERE name = 'vnpt_crash')
    WHEN sm.order_item_status = 303 AND sv.classification IN (1,2) AND sv.on_os_type = 0 AND sv.provider_type = 1
        THEN (SELECT id FROM vnpt_dev.states WHERE name = 'partner_crash')

    -- Nhóm 401 - 405 (phụ thuộc service) service thuộc loại SERVICE hoặc DIGITAL
    WHEN sm.order_item_status = 401 AND sv.classification IN (1,2) AND sv.on_os_type = 1 AND sv.provider_type = 0
       THEN (SELECT id FROM vnpt_dev.states WHERE name = 'vnpt_os_saas_ordered')
    WHEN sm.order_item_status = 401 AND sv.classification IN (1,2) AND sv.on_os_type = 1 AND sv.provider_type = 2
       THEN (SELECT id FROM vnpt_dev.states WHERE name = 'third_party_os_saas_ordered')

    WHEN sm.order_item_status = 402 AND sv.classification IN (1,2) AND sv.on_os_type = 1 AND sv.provider_type = 0
       THEN (SELECT id FROM vnpt_dev.states WHERE name = 'vnpt_os_saas_received')
    WHEN sm.order_item_status = 402 AND sv.classification IN (1,2) AND sv.on_os_type = 1 AND sv.provider_type = 2
       THEN (SELECT id FROM vnpt_dev.states WHERE name = 'third_party_os_saas_received')

    WHEN sm.order_item_status = 403 AND sv.classification IN (1,2) AND sv.on_os_type = 1 AND sv.provider_type = 0
       THEN (SELECT id FROM vnpt_dev.states WHERE name = 'vnpt_os_saas_processing')
    WHEN sm.order_item_status = 403 AND sv.classification IN (1,2) AND sv.on_os_type = 1 AND sv.provider_type = 2
       THEN (SELECT id FROM vnpt_dev.states WHERE name = 'vnpt_os_saas_processing')

    WHEN sm.order_item_status = 404 AND sv.classification IN (1,2) AND sv.on_os_type = 1 AND sv.provider_type = 0
       THEN (SELECT id FROM vnpt_dev.states WHERE name = 'vnpt_os_saas_completed')
    WHEN sm.order_item_status = 404 AND sv.classification IN (1,2) AND sv.on_os_type = 1 AND sv.provider_type = 2
       THEN (SELECT id FROM vnpt_dev.states WHERE name = 'third_party_os_saas_completed')

    WHEN sm.order_item_status = 405 AND sv.classification IN (1,2) AND sv.on_os_type = 1 AND sv.provider_type = 0
       THEN (SELECT id FROM vnpt_dev.states WHERE name = 'vnpt_os_saas_cancelled')
    WHEN sm.order_item_status = 405 AND sv.classification IN (1,2) AND sv.on_os_type = 1 AND sv.provider_type = 2
       THEN (SELECT id FROM vnpt_dev.states WHERE name = 'third_party_os_saas_cancelled')
    END
FROM vnpt_dev.subscriptions s
JOIN vnpt_dev.services sv ON s.service_id = sv.id
WHERE sm.subscription_id = s.id;

----- Cập nhật trạng thái của đơn hàng lắp đặt nhưng service là loại không lắp đặt => lùi về bước delivered nếu trong 3 trạng thái (207, 'waiting_installation'), (208, 'in_installation'), (209, 'installed')
update vnpt_dev.subscription_metadata
set state_id = data.new_state_id
    from (
        select
        subscription_metadata.subscription_id,
        (select id from vnpt_dev.states where name in ('delivered')) as new_state_id
        from vnpt_dev.subscriptions
            join vnpt_dev.subscription_metadata on subscriptions.id = subscription_metadata.subscription_id
            join vnpt_dev.services on subscriptions.service_id = services.id
            join vnpt_dev.product_orders on product_orders.id = subscription_metadata.product_order_id
        where
            services.installation_configuration = 0 and product_orders.has_installation = true
                and subscription_metadata.order_item_status in (207, 208, 209)
) as data
where
    data.subscription_id = subscription_metadata.subscription_id;

-- == Cập nhật lịch sử chuyển đổi của các thuê bao order hiện tại
WITH state_map AS (
    SELECT * FROM (
                      VALUES
                          (101, 'requested_warehouse'),
                          (102, 'preparing_stock'),
                          (103, 'ready_for_shipping'),
                          (104, 'pending_shipping'),
                          (105, 'shipped'),
                          (106, 'delivered'),
                          (107, 'online_verified'),
                          (108, 'cancelled'),

                          (201, 'requested_warehouse'),
                          (202, 'preparing_stock'),
                          (203, 'ready_for_shipping'),
                          (204, 'pending_shipping'),
                          (205, 'shipped'),
                          (206, 'delivered'),
                          (207, 'waiting_installation'),
                          (208, 'in_installation'),
                          (209, 'installed'),
                          (210, 'online_verified'),
                          (211, 'cancelled')
                  ) AS t(order_item_status, state_name)
),
     mapped_history AS (
         SELECT
             soh.id AS soh_id,
             soh.subscription_id,
             sm.state_transition_id,
             -- nếu previous_status = -1 thì pre_state_id = NULL
             CASE
                 WHEN soh.previous_status = -1 THEN NULL
                 -- ==== logic 301–303 ====
                 WHEN soh.previous_status = 301
                     AND sv.classification IN (1,2) AND sv.on_os_type = 0 AND sv.provider_type = 0
                     THEN (SELECT id FROM vnpt_dev.states WHERE name = 'vnpt_installing')
                 WHEN soh.previous_status = 301
                     AND sv.classification IN (1,2) AND sv.on_os_type = 0 AND sv.provider_type = 1
                     THEN (SELECT id FROM vnpt_dev.states WHERE name = 'partner_installing')

                 WHEN soh.previous_status = 302
                     AND sv.classification IN (1,2) AND sv.on_os_type = 0 AND sv.provider_type = 0
                     THEN (SELECT id FROM vnpt_dev.states WHERE name = 'vnpt_installed')
                 WHEN soh.previous_status = 302
                     AND sv.classification IN (1,2) AND sv.on_os_type = 0 AND sv.provider_type = 1
                     THEN (SELECT id FROM vnpt_dev.states WHERE name = 'partner_installed')

                 WHEN soh.previous_status = 303
                     AND sv.classification IN (1,2) AND sv.on_os_type = 0 AND sv.provider_type = 0
                     THEN (SELECT id FROM vnpt_dev.states WHERE name = 'vnpt_crash')
                 WHEN soh.previous_status = 303
                     AND sv.classification IN (1,2) AND sv.on_os_type = 0 AND sv.provider_type = 1
                     THEN (SELECT id FROM vnpt_dev.states WHERE name = 'partner_crash')

                 -- ==== logic 401–405 ====
                 WHEN soh.previous_status = 401
                     AND sv.classification IN (1,2) AND sv.on_os_type = 1 AND sv.provider_type = 0
                     THEN (SELECT id FROM vnpt_dev.states WHERE name = 'vnpt_os_saas_ordered')
                 WHEN soh.previous_status = 401
                     AND sv.classification IN (1,2) AND sv.on_os_type = 1 AND sv.provider_type = 2
                     THEN (SELECT id FROM vnpt_dev.states WHERE name = 'third_party_os_saas_ordered')

                 WHEN soh.previous_status = 402
                     AND sv.classification IN (1,2) AND sv.on_os_type = 1 AND sv.provider_type = 0
                     THEN (SELECT id FROM vnpt_dev.states WHERE name = 'vnpt_os_saas_received')
                 WHEN soh.previous_status = 402
                     AND sv.classification IN (1,2) AND sv.on_os_type = 1 AND sv.provider_type = 2
                     THEN (SELECT id FROM vnpt_dev.states WHERE name = 'third_party_os_saas_received')

                 WHEN soh.previous_status = 403
                     AND sv.classification IN (1,2) AND sv.on_os_type = 1 AND sv.provider_type = 0
                     THEN (SELECT id FROM vnpt_dev.states WHERE name = 'vnpt_os_saas_processing')
                 WHEN soh.previous_status = 403
                     AND sv.classification IN (1,2) AND sv.on_os_type = 1 AND sv.provider_type = 2
                     THEN (SELECT id FROM vnpt_dev.states WHERE name = 'third_party_os_saas_processing')

                 WHEN soh.previous_status = 404
                     AND sv.classification IN (1,2) AND sv.on_os_type = 1 AND sv.provider_type = 0
                     THEN (SELECT id FROM vnpt_dev.states WHERE name = 'vnpt_os_saas_completed')
                 WHEN soh.previous_status = 404
                     AND sv.classification IN (1,2) AND sv.on_os_type = 1 AND sv.provider_type = 2
                     THEN (SELECT id FROM vnpt_dev.states WHERE name = 'third_party_os_saas_completed')

                 WHEN soh.previous_status = 405
                     AND sv.classification IN (1,2) AND sv.on_os_type = 1 AND sv.provider_type = 0
                     THEN (SELECT id FROM vnpt_dev.states WHERE name = 'vnpt_os_saas_cancelled')
                 WHEN soh.previous_status = 405
                     AND sv.classification IN (1,2) AND sv.on_os_type = 1 AND sv.provider_type = 2
                     THEN (SELECT id FROM vnpt_dev.states WHERE name = 'third_party_os_saas_cancelled')
                 ELSE s_pre.id
                 END AS pre_state_id,

             CASE
                 -- ==== logic 301–303 ====
                 WHEN soh.next_status = 301
                     AND sv.classification IN (1,2) AND sv.on_os_type = 0 AND sv.provider_type = 0
                     THEN (SELECT id FROM vnpt_dev.states WHERE name = 'vnpt_installing')
                 WHEN soh.next_status = 301
                     AND sv.classification IN (1,2) AND sv.on_os_type = 0 AND sv.provider_type = 1
                     THEN (SELECT id FROM vnpt_dev.states WHERE name = 'partner_installing')

                 WHEN soh.next_status = 302
                     AND sv.classification IN (1,2) AND sv.on_os_type = 0 AND sv.provider_type = 0
                     THEN (SELECT id FROM vnpt_dev.states WHERE name = 'vnpt_installed')
                 WHEN soh.next_status = 302
                     AND sv.classification IN (1,2) AND sv.on_os_type = 0 AND sv.provider_type = 1
                     THEN (SELECT id FROM vnpt_dev.states WHERE name = 'partner_installed')

                 WHEN soh.next_status = 303
                     AND sv.classification IN (1,2) AND sv.on_os_type = 0 AND sv.provider_type = 0
                     THEN (SELECT id FROM vnpt_dev.states WHERE name = 'vnpt_crash')
                 WHEN soh.next_status = 303
                     AND sv.classification IN (1,2) AND sv.on_os_type = 0 AND sv.provider_type = 1
                     THEN (SELECT id FROM vnpt_dev.states WHERE name = 'partner_crash')

                 -- ==== logic 401–405 ====
                 WHEN soh.next_status = 401
                     AND sv.classification IN (1,2) AND sv.on_os_type = 1 AND sv.provider_type = 0
                     THEN (SELECT id FROM vnpt_dev.states WHERE name = 'vnpt_os_saas_ordered')
                 WHEN soh.next_status = 401
                     AND sv.classification IN (1,2) AND sv.on_os_type = 1 AND sv.provider_type = 2
                     THEN (SELECT id FROM vnpt_dev.states WHERE name = 'third_party_os_saas_ordered')

                 WHEN soh.next_status = 402
                     AND sv.classification IN (1,2) AND sv.on_os_type = 1 AND sv.provider_type = 0
                     THEN (SELECT id FROM vnpt_dev.states WHERE name = 'vnpt_os_saas_received')
                 WHEN soh.next_status = 402
                     AND sv.classification IN (1,2) AND sv.on_os_type = 1 AND sv.provider_type = 2
                     THEN (SELECT id FROM vnpt_dev.states WHERE name = 'third_party_os_saas_received')

                 WHEN soh.next_status = 403
                     AND sv.classification IN (1,2) AND sv.on_os_type = 1 AND sv.provider_type = 0
                     THEN (SELECT id FROM vnpt_dev.states WHERE name = 'vnpt_os_saas_processing')
                 WHEN soh.next_status = 403
                     AND sv.classification IN (1,2) AND sv.on_os_type = 1 AND sv.provider_type = 2
                     THEN (SELECT id FROM vnpt_dev.states WHERE name = 'third_party_os_saas_processing')

                 WHEN soh.next_status = 404
                     AND sv.classification IN (1,2) AND sv.on_os_type = 1 AND sv.provider_type = 0
                     THEN (SELECT id FROM vnpt_dev.states WHERE name = 'vnpt_os_saas_completed')
                 WHEN soh.next_status = 404
                     AND sv.classification IN (1,2) AND sv.on_os_type = 1 AND sv.provider_type = 2
                     THEN (SELECT id FROM vnpt_dev.states WHERE name = 'third_party_os_saas_completed')

                 WHEN soh.next_status = 405
                     AND sv.classification IN (1,2) AND sv.on_os_type = 1 AND sv.provider_type = 0
                     THEN (SELECT id FROM vnpt_dev.states WHERE name = 'vnpt_os_saas_cancelled')
                 WHEN soh.next_status = 405
                     AND sv.classification IN (1,2) AND sv.on_os_type = 1 AND sv.provider_type = 2
                     THEN (SELECT id FROM vnpt_dev.states WHERE name = 'third_party_os_saas_cancelled')
                 ELSE s_next.id
                 END AS state_id,
             soh.created_at,
             soh.user_id AS created_by
         FROM vnpt_dev.subscription_order_status_history soh
                  JOIN vnpt_dev.subscription_metadata sm ON sm.subscription_id = soh.subscription_id
                  JOIN vnpt_dev.subscriptions sub ON sub.id = soh.subscription_id
                  JOIN vnpt_dev.services sv ON sub.service_id = sv.id
                  LEFT JOIN state_map m_pre ON m_pre.order_item_status = soh.previous_status
                  LEFT JOIN state_map m_next ON m_next.order_item_status = soh.next_status
                  LEFT JOIN vnpt_dev.states s_pre ON s_pre.name = m_pre.state_name
                  LEFT JOIN vnpt_dev.states s_next ON s_next.name = m_next.state_name
     )
INSERT INTO vnpt_dev.state_transition_history (
    object_id,
    pre_state_id,
    state_id,
    created_at,
    created_by,
    trigger_type,
    object_type,
    state_transition_item_id
)
SELECT
    mh.subscription_id AS object_id,
    mh.pre_state_id,
    mh.state_id,
    mh.created_at,
    mh.created_by,
    'MANUAL' AS trigger_type,
    'ORDER' AS object_type,
    sti.id AS state_transition_item_id
FROM mapped_history mh
         JOIN vnpt_dev.state_transition_items sti
              ON sti.state_transition_id = mh.state_transition_id
                  AND sti.state_id = mh.state_id
         JOIN vnpt_dev.state_transition_item_triggers stit
              ON stit.state_transition_item_id = sti.id
                  AND (
                     (stit.pre_state_id IS NULL AND mh.pre_state_id IS NULL)
                         OR stit.pre_state_id = mh.pre_state_id
                     )
WHERE NOT EXISTS (
    SELECT 1
    FROM vnpt_dev.state_transition_history sth
    WHERE sth.object_id = mh.subscription_id
      AND sth.pre_state_id = mh.pre_state_id
      AND sth.state_id = mh.state_id
);
