package com.onedx.bos.dto.profiles;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.onedx.bos.constants.enums.ParameterTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_NULL)
public class CommandParamDTO {

    @Schema(name = "Tên tham số")
    private String name;
    @Schema(name = "Tên tham số hiển thị")
    private String displayName;
    @Schema(name = "Mô tả tham số")
    private String description;
    @Schema(name = "Kiểu dữ liệu tham số")
    private ParameterTypeEnum valueType;
    @Schema(name = "<PERSON><PERSON><PERSON> trị mặc định")
    private ParameterTypeEnum childType;
    @Schema(name = "Tham số bắt buôc")
    private Boolean mandatory;
    @Schema(name = "Hiển thị trên giao diện người dùng")
    private Boolean isDisplay;
    @Schema(name = "Cấu hình validate giá trị")
    private ValidationConfigDTO validations;
    @Schema(name = "Giá trị")
    private Object value;
    private Boolean isDefault;



    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(Include.NON_NULL)
    public static class ValidationConfigDTO {
        // dành cho kiểu Integer,Number
        private Float minValue;
        private Float maxValue;
        private Float defaultNumber;
        private String unit;


        // dành cho kiểu ký tự
        private Integer minLength;
        private Integer maxLength;
        private String defaultString;


        // dành cho kiểu liệt kê
//        private List<ListFormatDTO> values;

        // dành cho kiểu object
        private List<CommandParamDTO> values;
        private List<ListFormatDTO> valuesEnum;

        // dành cho kiểu array
        private Integer arrayLevel;
        private ParameterTypeEnum itemType;
    }
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ListFormatDTO {
        private String name;
        private String icon;
        private String description;
        private Boolean isDefault;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ObjectFormatDTO {
        String displayName;
        String key;
        ParameterTypeEnum type;
        String defaultValue;
        String description;
        List<ObjectFormatDTO> children;
    }






}
