package com.onedx.bos.service.profiles.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.onedx.bos.constants.enums.ActionTypeEnum;
import com.onedx.bos.constants.enums.CommandExecutionStatusEnum;
import com.onedx.bos.constants.enums.CommandTypeEnum;
import com.onedx.bos.constants.enums.DeletedFlag;
import com.onedx.bos.constants.enums.ParameterTypeEnum;
import com.onedx.bos.constants.exception.ErrorKey;
import com.onedx.bos.constants.exception.Resource;
import com.onedx.bos.dto.device.DeviceOverviewDto;
import com.onedx.bos.dto.deviceManager.GetDeviceDetailInfoDTO;
import com.onedx.bos.dto.deviceManager.GetDeviceTelemetryDTO;
import com.onedx.bos.dto.deviceManager.SendCommandReqDTO;
import com.onedx.bos.dto.profiles.*;
import com.onedx.bos.entity.DeviceCommandHistory;
import com.onedx.bos.entity.ProfileAttribute;
import com.onedx.bos.entity.ProfileCommand;
import com.onedx.bos.entity.ProfileTelemetry;
import com.onedx.bos.entity.Profiles;
import com.onedx.bos.repository.DeviceCommandHistoryRepository;
import com.onedx.bos.repository.ProfileAttributeRepository;
import com.onedx.bos.repository.ProfileCommandRepository;
import com.onedx.bos.repository.ProfileTelemetryRepository;
import com.onedx.bos.repository.ProfilesRepository;
import com.onedx.bos.service.device.DeviceService;
import com.onedx.bos.service.deviceManager.DeviceManagerService;
import com.onedx.bos.service.profileAuditHistory.ProfileAuditHistoryService;
import com.onedx.bos.service.profiles.ProfilesService;
import com.onedx.bos.utils.AuthUtil;
import com.onedx.common.constants.enums.Status;
import com.onedx.common.constants.values.CharacterConstant;
import com.onedx.common.exception.ExceptionFactory;
import com.onedx.common.exception.MessageKeyConstant;
import com.onedx.common.utils.ObjectMapperUtil;
import com.onedx.common.utils.SqlUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ProfilesServiceImpl implements ProfilesService {

    private final ProfilesRepository profilesRepository;
    private final ProfileCommandRepository profileCommandRepository;
    private final ObjectMapper objectMapper;
    private final ProfileTelemetryRepository profileTelemetryRepository;
    private final ExceptionFactory exceptionFactory;
    private final ProfileAttributeRepository profileAttributeRepository;
    private final ProfileAuditHistoryService profileAuditHistoryService;
    private final DeviceManagerService deviceManagerService;
    private final DeviceService deviceService;
    private final DeviceCommandHistoryRepository deviceCommandHistoryRepository;

    @Override
    public Page<IProfilesResponse> getListProfiles(String value, int isCode, int isName, int isModel, List<UUID> partnerList,
                                                   Pageable pageable) {
        String partnerListIdStr = partnerList.stream()
                .map(UUID::toString)
                .collect(Collectors.joining(","));
        value = SqlUtils.optimizeSearchLike(value);

        return profilesRepository.getListProfiles(partnerListIdStr, isCode, isName, isModel, value, pageable);
    }

    @Override
    @Transactional
    public void delete(List<Long> ids) {
        Date date = new Date();
        profilesRepository.deleteProfiles(ids, DeletedFlag.DELETED.getValue(), date);
    }

    @Override
    @Transactional
    public Long createProfile(ProfileCreateDTO profileCreateDTO) {

        validateProfile(profileCreateDTO, -1L);

        Long currentParentId = AuthUtil.getCurrentParentId();

        Profiles profiles = new Profiles();
        BeanUtils.copyProperties(profileCreateDTO, profiles);
        profiles.setPartnerUuid(UUID.fromString(profileCreateDTO.getPartnerUuid()));
        profiles.setStatus(Objects.nonNull(profileCreateDTO.getStatus()) ? profileCreateDTO.getStatus().value : Status.ACTIVE.value);
        profiles.setCreatedBy(currentParentId);
        profiles.setModifiedBy(currentParentId);
        profiles.setDeletedFlag(DeletedFlag.NOT_YET_DELETED.getValue());
        Profiles save = profilesRepository.save(profiles);

        // lưu danh sách lệnh điều khiển
        if (Objects.nonNull(profileCreateDTO.getControlCommands()) && !profileCreateDTO.getControlCommands().isEmpty()) {
            for (ControlCommandDTO controlCommand : profileCreateDTO.getControlCommands()) {
                createAndUpdateControlCommand(controlCommand, save, new ProfileCommand());
            }
        }
        // lưu cấu trúc dữ liệu
        if (Objects.nonNull(profileCreateDTO.getTelemetryData()) && !profileCreateDTO.getTelemetryData().isEmpty()) {
            for (TelemetryDataDTO telemetryData : profileCreateDTO.getTelemetryData()) {
                createAndUpdateTelemetryData(telemetryData, save, new ProfileTelemetry());
            }
        }
        // lưu thông tin thuộc tính
        if (Objects.nonNull(profileCreateDTO.getDeviceAttributes()) && !profileCreateDTO.getDeviceAttributes().isEmpty()) {
            for (DeviceAttributeDTO deviceAttribute : profileCreateDTO.getDeviceAttributes()) {
                createAndUpdateDeviceAttributes(deviceAttribute, save, new ProfileAttribute());
            }
        }
        // lưu log tạo
        profileAuditHistoryService.addLog(profiles, null, ActionTypeEnum.CREATE, CharacterConstant.BLANK);


        return save.getId();
    }

    @Override
    public Long updateProfile(ProfileCreateDTO profileCreateDTO) {

        validateProfile(profileCreateDTO, profileCreateDTO.getId());

        Profiles profile = profilesRepository.findById(profileCreateDTO.getId()).orElseThrow(
                () -> exceptionFactory.resourceNotFound(Resource.DEVICE_PROFILE, ErrorKey.ID, String.valueOf(profileCreateDTO.getId())));
        BeanUtils.copyProperties(profileCreateDTO, profile);
        Long currentParentId = AuthUtil.getCurrentParentId();
        profile.setModifiedBy(currentParentId);
        profile.setModifiedAt(new Date());
        profile.setPartnerUuid(UUID.fromString(profileCreateDTO.getPartnerUuid()));
        Profiles save = profilesRepository.save(profile);

        // TODO: cập nhật điều khiển lệnh trong profiles
        validateUniqueCommandNames(profileCreateDTO.getControlCommands());
        List<ProfileCommand> controlCommandDB = profileCommandRepository.findAllByProfileId(profileCreateDTO.getId());
        Map<Long, ProfileCommand> mapProfileCommand = controlCommandDB.stream()
                .collect(Collectors.toMap(ProfileCommand::getId, Function.identity()));
        for (ControlCommandDTO item : profileCreateDTO.getControlCommands()) {
            if (item.getId() == null) { // Tạo mới
                createAndUpdateControlCommand(item, save, new ProfileCommand());
            } else { // Chỉnh sửa
                ProfileCommand profileCommand = mapProfileCommand.get(item.getId());
                if (Objects.nonNull(profileCommand)) {
                    createAndUpdateControlCommand(item, save, profileCommand);
                }
            }
        }

        // TODO: cập nhật telemetry trong profiles
        validateUniqueTelemetryFields(profileCreateDTO.getTelemetryData());
        List<ProfileTelemetry> profileTelemetriesDB = profileTelemetryRepository.findAllByProfileId(profileCreateDTO.getId());
        Map<Long, ProfileTelemetry> mapProfileTelemetry = profileTelemetriesDB.stream()
                .collect(Collectors.toMap(ProfileTelemetry::getId, Function.identity()));
        for (TelemetryDataDTO item : profileCreateDTO.getTelemetryData()) {
            if (item.getId() == null) { // Tạo mới
                createAndUpdateTelemetryData(item, save, new ProfileTelemetry());
            } else { // Chỉnh sửa
                ProfileTelemetry profileTelemetry = mapProfileTelemetry.get(item.getId());
                if (Objects.nonNull(profileTelemetry)) {
                    createAndUpdateTelemetryData(item, save, profileTelemetry);
                }
            }
        }
        // TODO: cập nhật thuộc tính trong profiles
        validateUniqueAttributeFields(profileCreateDTO.getDeviceAttributes());
        List<ProfileAttribute> profileAttributesDB = profileAttributeRepository.findAllByProfileId(profileCreateDTO.getId());
        Map<Long, ProfileAttribute> mapProfileAttribute = profileAttributesDB.stream()
                .collect(Collectors.toMap(ProfileAttribute::getId, Function.identity()));
        for (DeviceAttributeDTO item : profileCreateDTO.getDeviceAttributes()) {
            if (item.getId() == null) { // Tạo mới
                createAndUpdateDeviceAttributes(item, save, new ProfileAttribute());
            } else { // Chỉnh sửa
                ProfileAttribute profileAttribute = mapProfileAttribute.get(item.getId());
                if (Objects.nonNull(profileAttribute)) {
                    createAndUpdateDeviceAttributes(item, save, profileAttribute);
                }
            }
        }
        // lưu log cập nhật
        if (Objects.nonNull(profileCreateDTO.getId())) {
            profileAuditHistoryService.addLog(profile, profileCreateDTO, ActionTypeEnum.UPDATE, CharacterConstant.BLANK);
        }

        return save.getId();
    }

    @Override
    public ProfileDetailDTO getDetailProfile(Long profileId) {
        IGetDetailProfile iGetDetailProfile = profilesRepository.getDetailProfile(profileId);
        ProfileDetailDTO profileDetail = new ProfileDetailDTO();
        BeanUtils.copyProperties(iGetDetailProfile, profileDetail);
        List<ControlCommandDetailDTO> commandDetailDTOS =
                Objects.nonNull(iGetDetailProfile.getCommandControl()) ? ObjectMapperUtil.listMapper(iGetDetailProfile.getCommandControl(),
                        ControlCommandDetailDTO.class) : Collections.emptyList();
        List<TelemetriesDataDetailDTO> telemetriesDataDetailDTOS =
                Objects.nonNull(iGetDetailProfile.getTelemetriesData()) ? ObjectMapperUtil.listMapper(iGetDetailProfile.getTelemetriesData(),
                        TelemetriesDataDetailDTO.class) : Collections.emptyList();
        List<DeviceAttributeDetailDTO> deviceAttributeDetailDTOS =
                Objects.nonNull(iGetDetailProfile.getDeviceAttribute()) ? ObjectMapperUtil.listMapper(iGetDetailProfile.getDeviceAttribute(),
                        DeviceAttributeDetailDTO.class) : Collections.emptyList();
        profileDetail.setControlCommandDetail(commandDetailDTOS);
        profileDetail.setTelemetriesDataDetail(telemetriesDataDetailDTOS);
        profileDetail.setDeviceAttributeDetail(deviceAttributeDetailDTOS);
        return profileDetail;
    }

    @Override
    public Page<IVersionListProfileDetail> getVersionListInProfileDetail(Long profileId, String value, Integer isProfileName, Integer isModel,
                                                                         String version, Long modifiedBy, Date startDate, Date endDate,
                                                                         Pageable pageable) {
        return profilesRepository.getVersionListInProfileDetail(profileId, value, isProfileName, isModel, version, modifiedBy, startDate,
                endDate, pageable);
    }

    @Override
    public List<String> getVersionList() {
        return profilesRepository.getVersionList();
    }

    @Override
    public List<CommonValueDTO> getModifiedByList() {
        return profilesRepository.getModifiedByList();
    }


    private void createAndUpdateControlCommand(ControlCommandDTO controlCommand, Profiles profiles, ProfileCommand profileCommand) {
        BeanUtils.copyProperties(controlCommand, profileCommand);
        profileCommand.setProfile(profiles);
        List<Map<String, CommandParamDTO>> commandParamList = new ArrayList<>();
        if (Objects.isNull(controlCommand.getCommandParams()) || controlCommand.getCommandParams().isEmpty()) {
            profileCommand.setParams(Collections.emptyList().toString());
        } else {
            for (CommandParamDTO commandParam : controlCommand.getCommandParams()) {
                Map<String, CommandParamDTO> commandParamMap = new HashMap<>();
                commandParamMap.put(commandParam.getName(), commandParam);
                commandParamList.add(commandParamMap);
            }
            try {
                String controlCommandJson = objectMapper.writeValueAsString(commandParamList);
                profileCommand.setParams(controlCommandJson);
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        }
        profileCommandRepository.save(profileCommand);

    }

    private void createAndUpdateTelemetryData(TelemetryDataDTO telemetryData, Profiles profiles, ProfileTelemetry profileTelemetry) {
        BeanUtils.copyProperties(telemetryData, profileTelemetry);
        profileTelemetry.setProfile(profiles);
        profileTelemetryRepository.save(profileTelemetry);
    }

    private void createAndUpdateDeviceAttributes(DeviceAttributeDTO deviceAttribute, Profiles profiles, ProfileAttribute profileAttribute) {
        BeanUtils.copyProperties(deviceAttribute, profileAttribute);
        profileAttribute.setProfile(profiles);
        profileAttributeRepository.save(profileAttribute);
    }

    private void validateProfile(ProfileCreateDTO request, Long id) {
        // validate model
        validateModel(request.getModel(), id);

        // validate name
        validateName(request.getName(), id);
    }

    private void validateName(String name, Long id) {
        if (profilesRepository.existsByNameAndDeletedFlagAndIdNot(name, DeletedFlag.NOT_YET_DELETED.getValue(), id)) {
            throw exceptionFactory.badRequest(MessageKeyConstant.ProfileDevice.PROFILE_NAME_EXIST, ErrorKey.PROFILE,
                    ErrorKey.PROFILES.PROFILE_NAME);
        }
    }

    private void validateModel(String model, Long id) {
        if (profilesRepository.existsByModelAndDeletedFlagAndIdNot(model, (short) DeletedFlag.NOT_YET_DELETED.getValue(), id)) {
            throw exceptionFactory.badRequest(MessageKeyConstant.ProfileDevice.PROFILE_MODEL_EXIST, ErrorKey.PROFILE,
                    ErrorKey.PROFILES.PROFILE_MODEL);
        }
    }

    public void validateUniqueCommandNames(List<ControlCommandDTO> commands) {
        if (commands == null || commands.isEmpty()) {
            return;
        }
        Set<String> seenNames = new HashSet<>();
        List<String> duplicateNames = commands.stream()
                .map(ControlCommandDTO::getName)
                .filter(Objects::nonNull)
                .map(String::trim)
                .filter(name -> !seenNames.add(name))
                .collect(Collectors.toList());
        if (!duplicateNames.isEmpty()) {
            throw exceptionFactory.dataInvalid(MessageKeyConstant.ProfileDevice.PROFILE_COMMAND_NAME_EXIST, Resource.DEVICE_PROFILE,
                    ErrorKey.PROFILE_COMMAND, ErrorKey.PROFILES.PROFILES_COMMAND.NAME);
        }
    }

    public void validateUniqueTelemetryFields(List<TelemetryDataDTO> telemetryList) {
        if (telemetryList == null || telemetryList.isEmpty()) {
            return;
        }
        // Check trùng name
        Set<String> seenNames = new HashSet<>();
        Optional<String> duplicateName = telemetryList.stream()
                .map(TelemetryDataDTO::getName)
                .filter(Objects::nonNull)
                .map(String::trim)
                .filter(name -> !seenNames.add(name))
                .findFirst();

        if (duplicateName.isPresent()) {
            throw exceptionFactory.dataInvalid(MessageKeyConstant.ProfileDevice.PROFILE_TELEMETRY_NAME_EXIST, Resource.DEVICE_PROFILE,
                    ErrorKey.PROFILE_TELEMETRY, ErrorKey.PROFILES.PROFILES_COMMAND.NAME);
        }

        // Check trùng displayName
        Set<String> seenDisplayNames = new HashSet<>();
        Optional<String> duplicateDisplayName = telemetryList.stream()
                .map(TelemetryDataDTO::getDisplayName)
                .filter(Objects::nonNull)
                .map(String::trim)
                .filter(displayName -> !seenDisplayNames.add(displayName))
                .findFirst();
        if (duplicateDisplayName.isPresent()) {
            throw exceptionFactory.dataInvalid(MessageKeyConstant.ProfileDevice.PROFILE_TELEMETRY_DISPLAY_NAME_EXIST, Resource.DEVICE_PROFILE,
                    ErrorKey.PROFILE_TELEMETRY, ErrorKey.PROFILES.PROFILES_COMMAND.DISPLAY_NAME);
        }
    }

    public void validateUniqueAttributeFields(List<DeviceAttributeDTO> deviceAttributes) {
        if (deviceAttributes == null || deviceAttributes.isEmpty()) {
            return;
        }
        // Check trùng name
        Set<String> seenNames = new HashSet<>();
        Optional<String> duplicateName = deviceAttributes.stream()
                .map(DeviceAttributeDTO::getName)
                .filter(Objects::nonNull)
                .map(String::trim)
                .filter(name -> !seenNames.add(name))
                .findFirst();

        if (duplicateName.isPresent()) {
            throw exceptionFactory.dataInvalid(MessageKeyConstant.ProfileDevice.PROFILE_ATTRIBUTE_NAME_EXIST, Resource.DEVICE_PROFILE,
                    ErrorKey.PROFILE_ATTRIBUTE, ErrorKey.PROFILES.PROFILES_COMMAND.NAME);
        }

        // Check trùng displayName
        Set<String> seenDisplayNames = new HashSet<>();
        Optional<String> duplicateDisplayName = deviceAttributes.stream()
                .map(DeviceAttributeDTO::getDisplayName)
                .filter(Objects::nonNull)
                .map(String::trim)
                .filter(displayName -> !seenDisplayNames.add(displayName))
                .findFirst();
        if (duplicateDisplayName.isPresent()) {
            throw exceptionFactory.dataInvalid(MessageKeyConstant.ProfileDevice.PROFILE_ATTRIBUTE_DISPLAY_NAME_EXIST, Resource.DEVICE_PROFILE,
                    ErrorKey.PROFILE_ATTRIBUTE, ErrorKey.PROFILES.PROFILES_COMMAND.DISPLAY_NAME);
        }
    }

    @Override
    public List<DeviceAttributeDTO> getIotPlatformAttributes() {
        String json = profilesRepository.getParamTextValueByParamType("IOT_PLATFORM_ATTRIBUTES");
        if (json == null || json.trim().isEmpty()) {
            return Collections.emptyList();
        }
        try {
            List<DeviceAttributeDTO> result = objectMapper.readValue(json, new TypeReference<List<DeviceAttributeDTO>>() {
            });
            return result;
        } catch (Exception e) {
            // Trường hợp dữ liệu không đúng định dạng JSON mảng đối tượng
            return Collections.emptyList();
        }
    }

    public void test() {
        List<ProfileAttribute> profileAttributes = profileAttributeRepository.findAllByProfileId(20L);
        List<DeviceAttributeDTO> attributes = profileAttributes.stream()
                .map(item -> {
                    DeviceAttributeDTO deviceAttributeDTO = new DeviceAttributeDTO();
                    BeanUtils.copyProperties(item, deviceAttributeDTO);
                    return deviceAttributeDTO;
                })
                .collect(Collectors.toList());
        getAttributesInIotPlatform(attributes, "Sec5227db-070d-428d-84dd-f3f81e08f05f");
    }

    @Override
    public List<DeviceAttributeDTO> getDeviceProfileData(String iotPlatformId, long profileId) {
        List<ProfileAttribute> profileAttributes = profileAttributeRepository.findAllByProfileId(profileId);
        List<DeviceAttributeDTO> attributes = profileAttributes.stream()
                .map(item -> {
                    DeviceAttributeDTO deviceAttributeDTO = new DeviceAttributeDTO();
                    BeanUtils.copyProperties(item, deviceAttributeDTO);
                    return deviceAttributeDTO;
                })
                .collect(Collectors.toList());
        getAttributesInIotPlatform(attributes, iotPlatformId);
        return attributes;
    }

    public void testTelemetry() {
        List<ProfileTelemetry> profileTelemetries = profileTelemetryRepository.findAllByProfileId(20L);
        List<TelemetryDataDTO> telemetryData = profileTelemetries.stream()
                .map(item -> {
                    TelemetryDataDTO telemetryDataDTO = new TelemetryDataDTO();
                    BeanUtils.copyProperties(item, telemetryDataDTO);
                    return telemetryDataDTO;
                })
                .collect(Collectors.toList());
        System.out.printf("telemetryData: %s", telemetryData);
        getTelemetryInIotPlatform(telemetryData, "Sec5227db-070d-428d-84dd-f3f81e08f05f");
    }

    @Override
    public List<TelemetryDataDTO> getDeviceTelemetryData(String iotPlatformId, long profileId) {
        List<ProfileTelemetry> profileTelemetries = profileTelemetryRepository.findAllByProfileId(profileId);
        List<TelemetryDataDTO> telemetryData = profileTelemetries.stream()
                .map(item -> {
                    TelemetryDataDTO telemetryDataDTO = new TelemetryDataDTO();
                    BeanUtils.copyProperties(item, telemetryDataDTO);
                    return telemetryDataDTO;
                })
                .collect(Collectors.toList());
        getTelemetryInIotPlatform(telemetryData, iotPlatformId);
        return telemetryData;
    }

    @Override
    public Boolean validateProfileName(String name, Long id) {
        return profilesRepository.existsByNameAndDeletedFlagAndIdNot(name, DeletedFlag.NOT_YET_DELETED.getValue(), id);
    }

    @Override
    public Boolean validateProfileModel(String model, Long id) {
        return profilesRepository.existsByModelAndDeletedFlagAndIdNot(model, DeletedFlag.NOT_YET_DELETED.getValue(), id);
    }

    public void getTelemetryInIotPlatform(List<TelemetryDataDTO> telemetryData, String deviceId) {
        GetDeviceTelemetryDTO getDeviceTelemetryDTO = deviceManagerService.getTelemetry(deviceId);
        if (telemetryData == null || telemetryData.isEmpty() || getDeviceTelemetryDTO == null) {
            return;
        }
        com.onedx.common.utils.DynamicMapperUtils.fillByNamePathFromJson(
                getDeviceTelemetryDTO.getContent(),
                telemetryData,
                TelemetryDataDTO::getName,
                TelemetryDataDTO::setValue
        );
    }

    public void getAttributesInIotPlatform(List<DeviceAttributeDTO> attributes, String deviceId) {
        GetDeviceDetailInfoDTO getDeviceDetailInfoDTO = deviceManagerService.getDeviceDetailInfo(deviceId);
        if (attributes == null || attributes.isEmpty() || getDeviceDetailInfoDTO == null) {
            return;
        }
        com.onedx.common.utils.DynamicMapperUtils.fillByNamePath(
                getDeviceDetailInfoDTO,
                attributes,
                DeviceAttributeDTO::getName,
                DeviceAttributeDTO::setValue
        );
    }

    public void testCommand() {
        List<ProfileCommand> profileCommands = profileCommandRepository.findAllByProfileId(29L);
        List<ControlCommandDTO> controlCommandDTOS = profileCommands.stream()
                .map(item -> {
                    ControlCommandDTO controlCommandDTO = new ControlCommandDTO();
                    BeanUtils.copyProperties(item, controlCommandDTO);
                    return controlCommandDTO;
                })
                .collect(Collectors.toList());
        System.out.printf("controlCommandDTOS: %s", controlCommandDTOS);
    }

    public void sendCommandToDevice(Long deviceId, ControlCommandDTO controlCommandDTO) {
        DeviceOverviewDto deviceOverviewDto = deviceService.getOverviewDevice(deviceId);
        if (Objects.isNull(deviceOverviewDto) || Objects.isNull(deviceOverviewDto.getIdentifier()) ||
                Objects.isNull(deviceOverviewDto.getIdentifier().getIdPlatform())) {
            return;
        }
        if (controlCommandDTO == null) {
            return;
        }
        // If command id is missing, resolve it using device's profile (by model) and command name
        if (controlCommandDTO.getId() == null) {
            String model = deviceOverviewDto.getModel();
            if (model != null && controlCommandDTO.getName() != null) {
                Profiles profile = profilesRepository.findTopByModelOrderByModifiedAtDesc(model);
                if (profile != null) {
                    profileCommandRepository.findByProfileIdAndName(profile.getId(), controlCommandDTO.getName())
                            .ifPresent(pc -> controlCommandDTO.setId(pc.getId()));
                }
            }
        }
        if (controlCommandDTO.getId() == null) {
            // still not resolved
            return;
        }
        profileCommandRepository.findById(controlCommandDTO.getId()).ifPresent(profileCommand -> {
            if (profileCommand.getType().equals(CommandTypeEnum.BUILT_IN)) {
                SendCommandReqDTO sendCommandReqDTO = new SendCommandReqDTO();
                sendCommandReqDTO.setCommandId(profileCommand.getName());
                sendCommandReqDTO.setName(profileCommand.getName());
                sendCommandReqDTO.setRecordId(String.valueOf(System.currentTimeMillis()));
                CommandParamDTO commandParamDTO = parseCommandParam(profileCommand.getParams());
                String data = buildDataCommand(commandParamDTO);
                sendCommandReqDTO.setData(data);
                System.out.printf("sendCommandReqDTO: %s", sendCommandReqDTO);
                String status = deviceManagerService.sendCommand(deviceOverviewDto.getIdentifier().getIdPlatform(), sendCommandReqDTO);
                DeviceCommandHistory deviceCommandHistory = new DeviceCommandHistory();
                deviceCommandHistory.setDevice(deviceService.getDevice(deviceId));
                deviceCommandHistory.setCommand(profileCommand);
                deviceCommandHistory.setCreatedBy(AuthUtil.getCurrentUserId());
                deviceCommandHistory.setParams(data);
                if (status != null && status.equals("2001")) {
                    deviceCommandHistory.setStatus(CommandExecutionStatusEnum.SENT);
                    System.out.printf("sendCommand success");
                } else {
                    deviceCommandHistory.setStatus(CommandExecutionStatusEnum.FAIL);
                    System.out.printf("sendCommand failed");
                }
                deviceCommandHistoryRepository.save(deviceCommandHistory);
            }
            if (profileCommand.getType().equals(CommandTypeEnum.ADVANCE)) {
                // Handle advance command using values provided in request body
                SendCommandReqDTO sendCommandReqDTO = new SendCommandReqDTO();
                sendCommandReqDTO.setCommandId(profileCommand.getName());
                sendCommandReqDTO.setName(profileCommand.getName());
                sendCommandReqDTO.setRecordId(String.valueOf(System.currentTimeMillis()));
                List<CommandParamDTO> params = controlCommandDTO.getCommandParams();
                populateParamValuesFromDefaults(params, profileCommand.getParams());
                String data = buildDataFromParams(params);
                sendCommandReqDTO.setData(data);
                System.out.printf("send ADVANCE CommandReqDTO: %s", sendCommandReqDTO);

                String status = deviceManagerService.sendCommand(deviceOverviewDto.getIdentifier().getIdPlatform(), sendCommandReqDTO);

                DeviceCommandHistory deviceCommandHistory = new DeviceCommandHistory();
                deviceCommandHistory.setDevice(deviceService.getDevice(deviceId));
                deviceCommandHistory.setCommand(profileCommand);
                deviceCommandHistory.setCreatedBy(AuthUtil.getCurrentUserId());
                deviceCommandHistory.setParams(data);
                if (status != null && status.equals("2001")) {
                    deviceCommandHistory.setStatus(CommandExecutionStatusEnum.SENT);
                    System.out.printf("send ADVANCE command success");
                } else {
                    deviceCommandHistory.setStatus(CommandExecutionStatusEnum.FAIL);
                    System.out.printf("send ADVANCE command failed");
                }
                deviceCommandHistoryRepository.save(deviceCommandHistory);
            }
        });

    }

    private CommandParamDTO parseCommandParam(String paramsJson) {
        if (paramsJson == null || paramsJson.trim().isEmpty()) {
            return null;
        }
        try {
            // Case 1: Stored as an array of single-entry maps e.g. [{"switch":{...}}]
            List<Map<String, CommandParamDTO>> list = objectMapper.readValue(paramsJson,
                    new TypeReference<List<Map<String, CommandParamDTO>>>() {
                    });
            if (list != null && !list.isEmpty()) {
                Map<String, CommandParamDTO> firstMap = list.get(0);
                if (firstMap != null && !firstMap.isEmpty()) {
                    Map.Entry<String, CommandParamDTO> entry = firstMap.entrySet().iterator().next();
                    CommandParamDTO dto = entry.getValue();
                    if (dto != null && (dto.getName() == null || dto.getName().trim().isEmpty())) {
                        dto.setName(entry.getKey());
                    }
                    return dto;
                }
            }
        } catch (Exception ignore) {
            // try next shapes
        }
        try {
            // Case 2: Stored as a single map {"switch":{...}}
            Map<String, CommandParamDTO> map = objectMapper.readValue(paramsJson,
                    new TypeReference<Map<String, CommandParamDTO>>() {
                    });
            if (map != null && !map.isEmpty()) {
                Map.Entry<String, CommandParamDTO> entry = map.entrySet().iterator().next();
                CommandParamDTO dto = entry.getValue();
                if (dto != null && (dto.getName() == null || dto.getName().trim().isEmpty())) {
                    dto.setName(entry.getKey());
                }
                return dto;
            }
        } catch (Exception ignore) {
            // try next shapes
        }
        try {
            // Case 3: Direct DTO JSON
            return objectMapper.readValue(paramsJson, CommandParamDTO.class);
        } catch (Exception e) {
            return null;
        }
    }

    public String buildDataCommand(CommandParamDTO commandParamDTO) {
        if (commandParamDTO == null) {
            return "";
        }
        String name = commandParamDTO.getName();
        if (name == null || name.trim().isEmpty()) {
            return "";
        }
        Map<String, Object> root = new LinkedHashMap<>();
        Object value = null;
        CommandParamDTO.ValidationConfigDTO validations = commandParamDTO.getValidations();
        if (commandParamDTO.getValueType() == com.onedx.bos.constants.enums.ParameterTypeEnum.OBJECT) {
            // Build nested object from objectFormat
            List<CommandParamDTO> formats = validations != null ? validations.getValues() : null;
            if (formats != null && !formats.isEmpty()) {
                value = buildObjectFromFormat(formats);
            }
        } else if (commandParamDTO.getValueType() == com.onedx.bos.constants.enums.ParameterTypeEnum.ENUM) {
            // Choose default enum value if provided
            List<CommandParamDTO.ListFormatDTO> values = validations != null ? validations.getValuesEnum() : null;
            if (values != null) {
                CommandParamDTO.ListFormatDTO def = values.stream().filter(v -> Boolean.TRUE.equals(v.getIsDefault())).findFirst().orElse(null);
                if (def != null) {
                    value = def.getName();
                } else if (!values.isEmpty()) {
                    value = values.get(0).getName();
                }
            }
        } else {
            // STRING, NUMBER, INTEGER: per requirement use defaultNumber when available
            Float defaultNumber = validations != null ? validations.getDefaultNumber() : null;
            if (defaultNumber != null) {
                // For INTEGER specifically, cast to integer value
                if (commandParamDTO.getValueType() == com.onedx.bos.constants.enums.ParameterTypeEnum.INTEGER) {
                    value = defaultNumber.intValue();
                } else {
                    value = defaultNumber;
                }
            } else {
                // Fallback: for STRING, try defaultString
                if (commandParamDTO.getValueType() == com.onedx.bos.constants.enums.ParameterTypeEnum.STRING) {
                    String ds = validations != null ? validations.getDefaultString() : null;
                    if (ds != null) {
                        value = ds;
                    }
                }
            }
        }
        if (value == null) {
            return "";
        }
        root.put(name, value);
        try {
            return objectMapper.writeValueAsString(root);
        } catch (Exception e) {
            // As a safe fallback, return simple key:value string
            String valStr = (value instanceof String) ? ("\"" + value + "\"") : String.valueOf(value);
            return "{" + "\"" + name + "\"" + ":" + valStr + "}";
        }
    }

    private void populateParamValuesFromDefaults(List<CommandParamDTO> params, String profileParamsJson) {
        if (params == null || params.isEmpty()) return;
        Map<String, CommandParamDTO> defMap = parseProfileParamsToMap(profileParamsJson);
        for (CommandParamDTO p : params) {
            if (p == null) continue;
            if (p.getValue() != null) continue;
            String name = p.getName();
            CommandParamDTO source = (name != null && defMap != null) ? defMap.get(name) : null;
            CommandParamDTO.ValidationConfigDTO validations = (source != null && source.getValidations() != null)
                    ? source.getValidations() : p.getValidations();
            com.onedx.bos.constants.enums.ParameterTypeEnum type = (source != null && source.getValueType() != null)
                    ? source.getValueType() : p.getValueType();

            Object val = null;
            if (type == com.onedx.bos.constants.enums.ParameterTypeEnum.OBJECT) {
                List<CommandParamDTO> formats = (source != null && source.getValidations() != null)
                        ? source.getValidations().getValues() : (validations != null ? validations.getValues() : null);
                if (formats != null && !formats.isEmpty()) {
                    val = buildObjectFromFormat(formats);
                }
            } else if (type == com.onedx.bos.constants.enums.ParameterTypeEnum.ENUM) {
                List<CommandParamDTO.ListFormatDTO> values = validations != null ? validations.getValuesEnum() : null;
                if (values != null) {
                    CommandParamDTO.ListFormatDTO def = values.stream().filter(v -> Boolean.TRUE.equals(v.getIsDefault())).findFirst().orElse(null);
                    if (def != null) {
                        val = def.getName();
                    } else if (!values.isEmpty()) {
                        val = values.get(0).getName();
                    }
                }
            } else if (type == com.onedx.bos.constants.enums.ParameterTypeEnum.ARRAY) {
                // Xử lý cho kiểu ARRAY
                val = buildDefaultArrayValue(validations);
            } else {
                Float defaultNumber = validations != null ? validations.getDefaultNumber() : null;
                if (defaultNumber != null) {
                    if (type == com.onedx.bos.constants.enums.ParameterTypeEnum.INTEGER) {
                        val = defaultNumber.intValue();
                    } else {
                        val = defaultNumber;
                    }
                } else if (type == com.onedx.bos.constants.enums.ParameterTypeEnum.STRING) {
                    String ds = validations != null ? validations.getDefaultString() : null;
                    if (ds != null) {
                        val = ds;
                    }
                }
            }
            if (val != null) {
                p.setValue(val);
            }
        }
    }

    /**
     * Tạo giá trị mặc định cho kiểu ARRAY
     * @param validations cấu hình validation chứa thông tin về array
     * @return giá trị mặc định cho array (có thể là empty array hoặc array với giá trị mặc định)
     */
    private Object buildDefaultArrayValue(CommandParamDTO.ValidationConfigDTO validations) {
        if (validations == null) {
            return new ArrayList<>();
        }

        // Lấy thông tin về kiểu dữ liệu của phần tử trong array
        com.onedx.bos.constants.enums.ParameterTypeEnum itemType = validations.getItemType();
        Integer arrayLevel = validations.getArrayLevel();

        // Nếu không có thông tin về itemType, trả về empty array
        if (itemType == null) {
            return new ArrayList<>();
        }

        // Tạo array với một phần tử mặc định dựa trên itemType
        List<Object> defaultArray = new ArrayList<>();
        Object defaultItem = buildDefaultValueForType(itemType, validations);

        // Nếu có giá trị mặc định cho item, thêm vào array
        if (defaultItem != null) {
            defaultArray.add(defaultItem);
        }

        return defaultArray;
    }

    /**
     * Tạo giá trị mặc định cho một kiểu dữ liệu cụ thể
     * @param type kiểu dữ liệu
     * @param validations cấu hình validation
     * @return giá trị mặc định
     */
    private Object buildDefaultValueForType(com.onedx.bos.constants.enums.ParameterTypeEnum type,
                                          CommandParamDTO.ValidationConfigDTO validations) {
        if (type == null) return null;

        switch (type) {
            case STRING:
                return validations != null && validations.getDefaultString() != null
                    ? validations.getDefaultString() : "";
            case INTEGER:
                return validations != null && validations.getDefaultNumber() != null
                    ? validations.getDefaultNumber().intValue() : 0;
            case NUMBER:
                return validations != null && validations.getDefaultNumber() != null
                    ? validations.getDefaultNumber() : 0.0f;
            case ENUM:
                if (validations != null && validations.getValuesEnum() != null) {
                    List<CommandParamDTO.ListFormatDTO> values = validations.getValuesEnum();
                    CommandParamDTO.ListFormatDTO def = values.stream()
                        .filter(v -> Boolean.TRUE.equals(v.getIsDefault()))
                        .findFirst()
                        .orElse(null);
                    if (def != null) {
                        return def.getName();
                    } else if (!values.isEmpty()) {
                        return values.get(0).getName();
                    }
                }
                return null;
            case OBJECT:
                List<CommandParamDTO> formats = validations != null ? validations.getValues() : null;
                if (formats != null && !formats.isEmpty()) {
                    return buildObjectFromFormat(formats);
                }
                return new LinkedHashMap<>();
            case ARRAY:
                // Nested array - trả về empty array
                return new ArrayList<>();
            default:
                return null;
        }
    }

    private Map<String, CommandParamDTO> parseProfileParamsToMap(String paramsJson) {
        if (paramsJson == null || paramsJson.trim().isEmpty()) return Collections.emptyMap();
        Map<String, CommandParamDTO> result = new LinkedHashMap<>();
        try {
            // Shape 1: List<Map<String, CommandParamDTO>> e.g. [{"speed":{...}}, {"mode":{...}}]
            List<Map<String, CommandParamDTO>> list = objectMapper.readValue(paramsJson,
                    new TypeReference<List<Map<String, CommandParamDTO>>>() {
                    });
            if (list != null) {
                for (Map<String, CommandParamDTO> m : list) {
                    if (m == null || m.isEmpty()) continue;
                    Map.Entry<String, CommandParamDTO> entry = m.entrySet().iterator().next();
                    String key = entry.getKey();
                    CommandParamDTO dto = entry.getValue();
                    if (dto != null && (dto.getName() == null || dto.getName().trim().isEmpty())) {
                        dto.setName(key);
                    }
                    result.put(key, dto);
                }
                if (!result.isEmpty()) return result;
            }
        } catch (Exception ignore) {
        }
        try {
            // Shape 2: Map<String, CommandParamDTO> e.g. {"speed":{...}, "mode":{...}}
            Map<String, CommandParamDTO> map = objectMapper.readValue(paramsJson,
                    new TypeReference<Map<String, CommandParamDTO>>() {
                    });
            if (map != null && !map.isEmpty()) {
                for (Map.Entry<String, CommandParamDTO> e : map.entrySet()) {
                    CommandParamDTO dto = e.getValue();
                    if (dto != null && (dto.getName() == null || dto.getName().trim().isEmpty())) {
                        dto.setName(e.getKey());
                    }
                    result.put(e.getKey(), dto);
                }
                if (!result.isEmpty()) return result;
            }
        } catch (Exception ignore) {
        }
        try {
            // Shape 3: List<CommandParamDTO>
            List<CommandParamDTO> listDto = objectMapper.readValue(paramsJson,
                    new TypeReference<List<CommandParamDTO>>() {
                    });
            if (listDto != null) {
                for (CommandParamDTO dto : listDto) {
                    if (dto != null && dto.getName() != null) {
                        result.put(dto.getName(), dto);
                    }
                }
                if (!result.isEmpty()) return result;
            }
        } catch (Exception ignore) {
        }
        try {
            // Shape 4: Single DTO
            CommandParamDTO dto = objectMapper.readValue(paramsJson, CommandParamDTO.class);
            if (dto != null && dto.getName() != null) {
                result.put(dto.getName(), dto);
            }
        } catch (Exception ignore) {
        }
        return result;
    }

    private String buildDataFromParams(List<CommandParamDTO> params) {
        if (params == null || params.isEmpty()) {
            return "";
        }
        Map<String, Object> root = new LinkedHashMap<>();
        for (CommandParamDTO p : params) {
            if (p == null) continue;
            String name = p.getName();
            if (name == null || name.trim().isEmpty()) continue;
            Object val = p.getValue();
            // Only include when value is provided; skip nulls to avoid sending invalid fields
            if (val != null) {
                root.put(name, val);
            }
        }
        if (root.isEmpty()) {
            return "";
        }
        try {
            return objectMapper.writeValueAsString(root);
        } catch (Exception e) {
            // Fallback simple formatter
            StringBuilder sb = new StringBuilder();
            sb.append("{");
            boolean first = true;
            for (Map.Entry<String, Object> e1 : root.entrySet()) {
                if (!first) sb.append(",");
                first = false;
                Object v = e1.getValue();
                String valStr = (v instanceof String) ? ("\"" + v + "\"") : String.valueOf(v);
                sb.append("\"").append(e1.getKey()).append("\"").append(":").append(valStr);
            }
            sb.append("}");
            return sb.toString();
        }
    }

        private Map<String, Object> buildObjectFromFormat(List<CommandParamDTO> formats) {
        Map<String, Object> map = new LinkedHashMap<>();
        for (CommandParamDTO f : formats) {
            if (f == null || f.getName() == null || f.getName().trim().isEmpty()) {
                continue;
            }
            Object val;
            if (f.getValueType() == com.onedx.bos.constants.enums.ParameterTypeEnum.OBJECT) {
                List<CommandParamDTO> children = f.getValidations().getValues();
                val = (children != null && !children.isEmpty()) ? buildObjectFromFormat(children) : new LinkedHashMap<>();
            } else if (f.getValueType() == com.onedx.bos.constants.enums.ParameterTypeEnum.INTEGER) {
                try {
                    val = f.getValidations().getDefaultNumber() != null ? Integer.parseInt(String.valueOf(f.getValidations().getDefaultNumber())) : null;
                } catch (NumberFormatException ex) {
                    val = null;
                }
            } else if (f.getValueType() == com.onedx.bos.constants.enums.ParameterTypeEnum.NUMBER) {
                try {
                    val = f.getValidations().getDefaultNumber() != null ? Double.parseDouble(
                        String.valueOf(f.getValidations().getDefaultNumber())) : null;
                } catch (NumberFormatException ex) {
                    val = null;
                }
            } else {
                // STRING or others treated as string
                val = f.getValidations().getDefaultString();
            }
            if (val != null) {
                map.put(f.getName(), val);
            }
        }
        return map;
    }
}



